# Especificação de Projeto – **Tasks Events Manager**

> Documento vivo ➜ Depois de cada iteração, atualizar aqui para manter uma única fonte de verdade.

---

## 1. Visão Geral

Aplicativo mobile **Flutter** (inicialmente Web para testes) para **gerenciar tarefas (tasks) dentro de eventos** colaborativos.

* **Criador do evento** vira **<PERSON><PERSON><PERSON> Principal**.
* Outros usuários entram via **tag/ID** do evento e tornam‑se **Voluntários**.
* <PERSON><PERSON>nte cria **Tasks** e **Microtasks** e atribui voluntários manualmente ou via heurística (matching de habilidades, horários e recursos).

### Objetivos

1. Facilitar organização de eventos baseados em voluntariado.
2. Centralizar comunicação de demandas (tasks) e disponibilidade.
3. Permitir atribuição inteligente (no futuro) com base em dados de perfil.

---

## 2. <PERSON><PERSON>

| Camada                   | Tecnologias                                                     |
| ------------------------ | --------------------------------------------------------------- |
| **Frontend**             | Flutter 3.x (Dart) – Web & Mobile                               |
| **Backend‑as‑a‑Service** | Firebase (Auth, Cloud Firestore, Cloud Functions, Storage)      |
| **State Management**     | Provider + Riverpod ou Bloc (escolher 👉 *Decidir na Sprint 0*) |
| **Auth Social**          | Google Sign‑In (Firebase Auth)                                  |
| **CI/CD**                | GitHub Actions ➜ Deploy Firebase Hosting / Play Store (futuro)  |
| **Heurística**           | Cloud Function (Node.js/TypeScript) chamável; placeholder       |

---

## 3. Estrutura de Pastas (Flutter)

```
lib/
  core/
    constants/
    exceptions/
    models/        # Entidades (User, Event, Task, MicroTask)
    services/      # Abstrações para Firebase, Auth, etc.
    utils/
  features/
    auth/
      data/
      domain/
      presentation/
    events/
      data/
      domain/
      presentation/
    tasks/
      data/
      domain/
      presentation/
  shared_widgets/
  main.dart
```

*Adote separação **DDD (Data / Domain / Presentation)** para escalar.*

---

## 4. Modelagem de Dados (Cloud Firestore)

> Sugestão de coleções e subcoleções. Ajustar nomes conforme convenção camelCase.

### users (coleção)

| Campo           | Tipo                        | Notas                             |
| --------------- | --------------------------- | --------------------------------- |
| uid (docId)     | string                      | Id Firebase Auth                  |
| displayName     | string                      |                                   |
| email           | string                      |                                   |
| photoURL        | string                      |                                   |
| skills          | array<string>               |                                   |
| resources       | array<string>               |                                   |
| availability    | map\<string, array<string>> | `{ "Mon": ["18:00-22:00"], ... }` |
| managerInEvents | array<string>               | IDs de eventos onde é gerente     |

### events (coleção)

| Campo             | Tipo          | Notas                                   |
| ----------------- | ------------- | --------------------------------------- |
| eventId (docId)   | string        | Tag visível ao usuário (ex: EVT‑ABC123) |
| name              | string        |                                         |
| description       | string        |                                         |
| location          | string        |                                         |
| requiredSkills    | array<string> |                                         |
| requiredResources | array<string> |                                         |
| managers          | array<string> | `uid`s                                  |
| volunteers        | array<string> | `uid`s                                  |
| createdAt         | timestamp     |                                         |

#### Subcoleções

* **tasks** ➜ `/events/{eventId}/tasks/{taskId}`
  *Possível: microtasks como subcoleção de tasks.*

### tasks (subcoleção)

| Campo              | Tipo                               | Notas      |
| ------------------ | ---------------------------------- | ---------- |
| taskId (docId)     | string                             |            |
| title              | string                             |            |
| description        | string                             |            |
| status             | enum("open","in\_progress","done") |            |
| requiredSkills     | array<string>                      |            |
| requiredResources  | array<string>                      |            |
| assignedVolunteers | array<string>                      |            |
| createdBy          | string                             | uid        |
| microtasks         | collection                         | ver abaixo |

### microtasks (sub‑subcoleção)

| Campo               | Tipo    |
| ------------------- | ------- |
| microTaskId (docId) | string  |
| title               | string  |
| description         | string  |
| assignedVolunteer   | string? |
| status              | enum    |

> **Regra:** microtasks devem referenciar `parentTaskId` para consultas agregadas.

---

## 5. Fluxos de Usuário (Happy Path)

1. **Login**

   * Google Sign‑In ➜ Redireciona para *Home*.
2. **Home**

   * Lista cartões dos eventos onde o usuário é voluntário ou gerente.
   * *FloatingActionButton (+)* abre **Modal Ação**:

     * **Criar evento**
     * **Entrar via tag**
3. **Criar Evento**

   * Formulário ➜ Salvar em `events`. Adicionar usuário na lista `managers`.
4. **Entrar em Evento**

   * Campo Tag ➜ Verificar existência ➜ Adicionar a `volunteers`.
   * Usuário preenche *skills*, *resources*, *availability* específicas para esse evento.
5. **Dashboard Evento** (tabbed)

   1. **Evento** – Detalhes.
   2. **Criar Tasks** – Gerentes criam.
   3. **Gerenciar Voluntários** – Listagem + atribuição.
   4. **Acompanhar Tasks** – Kanban ou lista filtrável.
6. **Atribuição Heurística (MVP: manual)**

   * Botão em Task ➜ “Sugestão de voluntários” (placeholder) ➜ no futuro chama Cloud Function.

---

## 6. Regras de Negócio Essenciais

* Um **usuário** pode ser gerente em vários eventos.
* Gerente pode **promover** voluntário a gerente (`managers` array).
* Microtask precisa pertencer a uma Task.
* Voluntário só pode ver tasks do evento em que está vinculado.

---

## 7. Navegação (Router)

| Rota                           | Widget            |
| ------------------------------ | ----------------- |
| `/`                            | Splash / AuthGate |
| `/login`                       | LoginPage         |
| `/home`                        | HomePage          |
| `/event/:eventId`              | EventShell (Tabs) |
| `/event/:eventId/task/:taskId` | TaskDetailsPage   |

---

## 8. Autorização & Segurança (Rules v2)

* **events/{eventId}** – Read se `uid` ∈ managers ∪ volunteers.
* **events/{eventId}/tasks** – Write apenas managers.
* **microtasks** – Write se manager OU (assignedVolunteer == uid).

---

## 9. Pontos em Aberto / Próximos Passos

1. **Definir algoritmo heurístico** (matching score):

   * Similaridade de *skills*.
   * Interseção de disponibilidade horária.
   * Peso de recursos disponíveis.
2. **Escolher State Management** (Provider vs Bloc).
3. **Design System** (cores, tipografia, ícones).
4. **Notificações push** (Cloud Messaging).
5. **Offline support** (FireStore cache).

---

## 10. Glossário

| Termo          | Significado                                     |
| -------------- | ----------------------------------------------- |
| **Evento**     | Contexto que agrupa tasks (ex: feira, mutirão). |
| **Task**       | Atividade principal dentro de um evento.        |
| **Microtask**  | Sub‑atividade, granular.                        |
| **Gerente**    | Usuário com poderes de criação/atribuição.      |
| **Voluntário** | Usuário que executa tasks.                      |

---

## 11. Referências Rápidas

* [Firebase Docs](https://firebase.google.com/docs)
* [Flutter Fire](https://firebase.flutter.dev/)

> **Pronto!** Revise e anote comentários. Sinta‑se livre para pedir ajustes.
