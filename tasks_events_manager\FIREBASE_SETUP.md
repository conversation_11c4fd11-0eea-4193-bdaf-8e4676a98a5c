# Configuração do Firebase para Tasks Events Manager

## 🔥 Configuração Completa do Firebase

### 1. Criar Projeto Firebase

1. Acesse [Firebase Console](https://console.firebase.google.com)
2. Clique em "Criar um projeto"
3. Nome: `tasks-events-manager`
4. Ative Google Analytics (opcional)
5. Selecione conta do Analytics
6. Clique em "Criar projeto"

### 2. Configurar Authentication

1. No console Firebase, vá para **Authentication**
2. Clique em **Começar**
3. Vá para a aba **Sign-in method**
4. Ative **Google**:
   - Clique em Google
   - Ative o toggle
   - Configure o nome do projeto e email de suporte
   - Clique em **Salvar**

### 3. Configurar Firestore Database

1. Vá para **Firestore Database**
2. Clique em **Criar banco de dados**
3. Selecione **Iniciar no modo de teste** (por enquanto)
4. Escolha a localização (us-central1 recomendado)
5. Clique em **Concluído**

### 4. Configurar Web App

1. No console Firebase, clique no ícone **Web** (`</>`)
2. Nome do app: `tasks-events-manager-web`
3. Marque **"Configurar também o Firebase Hosting"** (opcional)
4. Clique em **Registrar app**
5. **IMPORTANTE**: Copie a configuração mostrada

### 5. Atualizar firebase_options.dart

Substitua o conteúdo de `lib/firebase_options.dart` com:

```dart
// File generated by FlutterFire CLI.
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'SUA_API_KEY_AQUI',
    appId: 'SEU_APP_ID_AQUI',
    messagingSenderId: 'SEU_SENDER_ID_AQUI',
    projectId: 'SEU_PROJECT_ID_AQUI',
    authDomain: 'SEU_PROJECT_ID.firebaseapp.com',
    storageBucket: 'SEU_PROJECT_ID.appspot.com',
  );

  // Adicione outras plataformas conforme necessário
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'SUA_ANDROID_API_KEY',
    appId: 'SEU_ANDROID_APP_ID',
    messagingSenderId: 'SEU_SENDER_ID',
    projectId: 'SEU_PROJECT_ID',
    storageBucket: 'SEU_PROJECT_ID.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'SUA_IOS_API_KEY',
    appId: 'SEU_IOS_APP_ID',
    messagingSenderId: 'SEU_SENDER_ID',
    projectId: 'SEU_PROJECT_ID',
    storageBucket: 'SEU_PROJECT_ID.appspot.com',
    iosBundleId: 'com.tasksevents.tasksEventsManager',
  );
}
```

### 6. Configurar Domínios Autorizados

1. No Firebase Console, vá para **Authentication > Settings**
2. Na seção **Authorized domains**, adicione:
   - `localhost` (para desenvolvimento)
   - Seu domínio de produção (quando deploy)

### 7. Regras de Segurança Firestore

1. Vá para **Firestore Database > Rules**
2. Substitua as regras por:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users podem ler/escrever seus próprios dados
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Events - participantes podem ler, gerentes podem escrever
    match /events/{eventId} {
      allow read: if request.auth != null && 
        (request.auth.uid in resource.data.managers || 
         request.auth.uid in resource.data.volunteers);
      allow write: if request.auth != null && 
        request.auth.uid in resource.data.managers;
        
      // Tasks como subcoleção
      match /tasks/{taskId} {
        allow read: if request.auth != null && 
          (request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.managers || 
           request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.volunteers);
        allow write: if request.auth != null && 
          request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.managers;
          
        // MicroTasks como subcoleção
        match /microtasks/{microTaskId} {
          allow read, write: if request.auth != null && 
            (request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.managers || 
             request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.volunteers);
        }
      }
    }
  }
}
```

3. Clique em **Publicar**

### 8. Testar a Configuração

1. Execute o app: `flutter run -d web-server --web-port=8080`
2. Acesse `http://localhost:8080`
3. Clique em "Entrar com Google"
4. Verifique se o login funciona corretamente
5. Teste criar um evento
6. Verifique no Firebase Console se os dados foram salvos

### 9. Configuração Avançada (Opcional)

#### Configurar Firebase CLI
```bash
npm install -g firebase-tools
firebase login
firebase init
```

#### Configurar FlutterFire CLI
```bash
dart pub global activate flutterfire_cli
flutterfire configure
```

### 🚨 Problemas Comuns

#### Erro: "Firebase project not found"
- Verifique se o projectId está correto
- Confirme se o projeto existe no Firebase Console

#### Erro: "API key not valid"
- Verifique se a API key está correta
- Confirme se as APIs necessárias estão ativadas

#### Erro: "Unauthorized domain"
- Adicione localhost aos domínios autorizados
- Para produção, adicione seu domínio real

#### Login não funciona
- Verifique se o Google Sign-In está ativado
- Confirme se o domínio está autorizado
- Teste em modo incógnito

### 📞 Suporte

Se encontrar problemas:
1. Verifique o console do navegador para erros
2. Consulte a documentação do Firebase
3. Verifique se todas as APIs estão ativadas no Google Cloud Console

### ✅ Checklist Final

- [ ] Projeto Firebase criado
- [ ] Authentication configurado com Google
- [ ] Firestore Database criado
- [ ] Web app registrado
- [ ] firebase_options.dart atualizado
- [ ] Domínios autorizados configurados
- [ ] Regras de segurança aplicadas
- [ ] App testado e funcionando
