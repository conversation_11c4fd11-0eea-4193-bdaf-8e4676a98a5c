import 'package:flutter/material.dart';

class EventShell extends StatefulWidget {
  final String eventId;

  const EventShell({
    super.key,
    required this.eventId,
  });

  @override
  State<EventShell> createState() => _EventShellState();
}

class _EventShellState extends State<EventShell> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Evento ${widget.eventId}'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.info), text: 'Evento'),
            Tab(icon: Icon(Icons.add_task), text: 'Criar Tasks'),
            Tab(icon: Icon(Icons.people), text: 'Volunt<PERSON><PERSON>s'),
            Tab(icon: Icon(Icons.track_changes), text: 'Acompanhar'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _EventDetailsTab(eventId: widget.eventId),
          _CreateTasksTab(eventId: widget.eventId),
          _ManageVolunteersTab(eventId: widget.eventId),
          _TrackTasksTab(eventId: widget.eventId),
        ],
      ),
    );
  }
}

class _EventDetailsTab extends StatelessWidget {
  final String eventId;

  const _EventDetailsTab({required this.eventId});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Detalhes do Evento',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}

class _CreateTasksTab extends StatelessWidget {
  final String eventId;

  const _CreateTasksTab({required this.eventId});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.add_task, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Criar Tasks',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}

class _ManageVolunteersTab extends StatelessWidget {
  final String eventId;

  const _ManageVolunteersTab({required this.eventId});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Gerenciar Voluntários',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}

class _TrackTasksTab extends StatelessWidget {
  final String eventId;

  const _TrackTasksTab({required this.eventId});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.track_changes, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Acompanhar Tasks',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}
