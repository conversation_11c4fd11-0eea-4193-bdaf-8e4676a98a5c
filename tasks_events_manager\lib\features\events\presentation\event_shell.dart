import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../domain/event_detail_provider.dart';
import '../../auth/domain/auth_provider.dart';
import '../../../core/models/event_model.dart';

class EventShell extends StatefulWidget {
  final String eventId;

  const EventShell({super.key, required this.eventId});

  @override
  State<EventShell> createState() => _EventShellState();
}

class _EventShellState extends State<EventShell> with TickerProviderStateMixin {
  late TabController _tabController;
  late EventDetailProvider _eventDetailProvider;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _eventDetailProvider = EventDetailProvider();
    _eventDetailProvider.loadEvent(widget.eventId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _eventDetailProvider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _eventDetailProvider,
      child: Consumer<EventDetailProvider>(
        builder: (context, eventProvider, child) {
          if (eventProvider.isLoading) {
            return Scaffold(
              appBar: AppBar(title: Text('Carregando...')),
              body: const Center(child: CircularProgressIndicator()),
            );
          }

          if (eventProvider.errorMessage != null) {
            return Scaffold(
              appBar: AppBar(title: const Text('Erro')),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      'Erro',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      eventProvider.errorMessage!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        eventProvider.clearError();
                        eventProvider.loadEvent(widget.eventId);
                      },
                      child: const Text('Tentar novamente'),
                    ),
                  ],
                ),
              ),
            );
          }

          final event = eventProvider.event;
          if (event == null) {
            return Scaffold(
              appBar: AppBar(title: const Text('Evento não encontrado')),
              body: const Center(child: Text('Evento não encontrado')),
            );
          }

          return Scaffold(
            appBar: AppBar(
              title: Text(event.name),
              bottom: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(icon: Icon(Icons.info), text: 'Evento'),
                  Tab(icon: Icon(Icons.add_task), text: 'Tasks'),
                  Tab(icon: Icon(Icons.people), text: 'Voluntários'),
                  Tab(icon: Icon(Icons.track_changes), text: 'Acompanhar'),
                ],
              ),
            ),
            body: TabBarView(
              controller: _tabController,
              children: [
                _EventDetailsTab(event: event),
                _CreateTasksTab(event: event),
                _ManageVolunteersTab(event: event),
                _TrackTasksTab(event: event),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _EventDetailsTab extends StatelessWidget {
  final EventModel event;

  const _EventDetailsTab({required this.event});

  @override
  Widget build(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    final isManager = event.isManager(authProvider.user?.uid ?? '');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          event.name,
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isManager
                              ? Colors.blue[100]
                              : Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          isManager ? 'GERENTE' : 'VOLUNTÁRIO',
                          style: TextStyle(
                            color: isManager
                                ? Colors.blue[800]
                                : Colors.green[800],
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ID: ${event.eventId}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontFamily: 'monospace',
                    ),
                  ),
                  if (event.description.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Descrição',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(event.description),
                  ],
                  if (event.location.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Local',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.location_on, size: 16),
                        const SizedBox(width: 4),
                        Expanded(child: Text(event.location)),
                      ],
                    ),
                  ],
                  const SizedBox(height: 16),
                  Text(
                    'Criado em',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(_formatDate(event.createdAt)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Participantes',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _StatCard(
                          icon: Icons.admin_panel_settings,
                          title: 'Gerentes',
                          value: event.managers.length.toString(),
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _StatCard(
                          icon: Icons.people,
                          title: 'Voluntários',
                          value: event.volunteers.length.toString(),
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

class _StatCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final Color color;

  const _StatCard({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: color),
          ),
        ],
      ),
    );
  }
}

class _CreateTasksTab extends StatelessWidget {
  final EventModel event;

  const _CreateTasksTab({required this.event});

  @override
  Widget build(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    final isManager = event.isManager(authProvider.user?.uid ?? '');

    if (!isManager) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Acesso Restrito',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 8),
            Text('Apenas gerentes podem criar tasks'),
          ],
        ),
      );
    }

    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.add_task, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Criar Tasks',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}

class _ManageVolunteersTab extends StatelessWidget {
  final EventModel event;

  const _ManageVolunteersTab({required this.event});

  @override
  Widget build(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    final isManager = event.isManager(authProvider.user?.uid ?? '');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isManager) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ações de Gerente',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Implementar promoção de voluntário
                            },
                            icon: const Icon(Icons.admin_panel_settings),
                            label: const Text('Promover Voluntário'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Implementar remoção de participante
                            },
                            icon: const Icon(Icons.person_remove),
                            label: const Text('Remover Participante'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Gerentes (${event.managers.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (event.managers.isEmpty)
                    const Text('Nenhum gerente encontrado')
                  else
                    ...event.managers.map(
                      (uid) => ListTile(
                        leading: const CircleAvatar(
                          child: Icon(Icons.admin_panel_settings),
                        ),
                        title: Text('Gerente $uid'),
                        subtitle: const Text('Gerente do evento'),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Voluntários (${event.volunteers.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (event.volunteers.isEmpty)
                    const Text('Nenhum voluntário encontrado')
                  else
                    ...event.volunteers.map(
                      (uid) => ListTile(
                        leading: const CircleAvatar(child: Icon(Icons.person)),
                        title: Text('Voluntário $uid'),
                        subtitle: const Text('Voluntário do evento'),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TrackTasksTab extends StatelessWidget {
  final EventModel event;

  const _TrackTasksTab({required this.event});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.track_changes, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Acompanhar Tasks',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text('Em desenvolvimento...'),
        ],
      ),
    );
  }
}
