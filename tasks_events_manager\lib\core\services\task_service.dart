import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';
import '../constants/firebase_constants.dart';

class TaskService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  // Criar nova task
  Future<TaskModel> createTask({
    required String eventId,
    required String title,
    required String description,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required String creatorUid,
  }) async {
    try {
      final taskId = _uuid.v4();
      
      final task = TaskModel(
        taskId: taskId,
        title: title,
        description: description,
        status: TaskStatus.open,
        requiredSkills: requiredSkills,
        requiredResources: requiredResources,
        assignedVolunteers: [],
        createdBy: creatorUid,
        createdAt: DateTime.now(),
        eventId: eventId,
      );

      // Salvar task como subcoleção do evento
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .set(task.toMap());

      return task;
    } catch (e) {
      print('Error creating task: $e');
      rethrow;
    }
  }

  // Buscar tasks de um evento
  Stream<List<TaskModel>> getEventTasks(String eventId) {
    return _firestore
        .collection(FirebaseConstants.eventsCollection)
        .doc(eventId)
        .collection(FirebaseConstants.tasksCollection)
        .orderBy(FirebaseConstants.createdAt, descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TaskModel.fromDocument(doc))
            .toList());
  }

  // Buscar task por ID
  Future<TaskModel?> getTaskById(String eventId, String taskId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .get();

      if (doc.exists) {
        return TaskModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting task: $e');
      return null;
    }
  }

  // Atribuir voluntário à task
  Future<bool> assignVolunteerToTask(
    String eventId,
    String taskId,
    String volunteerUid,
    String assignerUid,
  ) async {
    try {
      final taskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId);

      // Verificar se o assigner é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(assignerUid)) return false;

      // Verificar se o voluntário é participante do evento
      if (!event.isParticipant(volunteerUid)) return false;

      // Adicionar voluntário à task
      await taskRef.update({
        FirebaseConstants.assignedVolunteers: FieldValue.arrayUnion([volunteerUid]),
      });

      return true;
    } catch (e) {
      print('Error assigning volunteer to task: $e');
      return false;
    }
  }

  // Remover voluntário da task
  Future<bool> unassignVolunteerFromTask(
    String eventId,
    String taskId,
    String volunteerUid,
    String unassignerUid,
  ) async {
    try {
      final taskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId);

      // Verificar se o unassigner é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(unassignerUid)) return false;

      // Remover voluntário da task
      await taskRef.update({
        FirebaseConstants.assignedVolunteers: FieldValue.arrayRemove([volunteerUid]),
      });

      return true;
    } catch (e) {
      print('Error unassigning volunteer from task: $e');
      return false;
    }
  }

  // Atualizar status da task
  Future<bool> updateTaskStatus(
    String eventId,
    String taskId,
    TaskStatus newStatus,
    String updaterUid,
  ) async {
    try {
      final taskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId);

      // Verificar se o updater é gerente ou está atribuído à task
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      final taskDoc = await taskRef.get();
      
      if (!taskDoc.exists) return false;

      final task = TaskModel.fromDocument(taskDoc);

      // Verificar permissões
      if (!event.isManager(updaterUid) && !task.isAssignedTo(updaterUid)) {
        return false;
      }

      // Atualizar status
      await taskRef.update({
        FirebaseConstants.status: newStatus.value,
      });

      return true;
    } catch (e) {
      print('Error updating task status: $e');
      return false;
    }
  }

  // Atualizar task
  Future<bool> updateTask(
    String eventId,
    TaskModel task,
    String updaterUid,
  ) async {
    try {
      // Verificar se o updater é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(updaterUid)) return false;

      // Atualizar task
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(task.taskId)
          .update(task.toMap());

      return true;
    } catch (e) {
      print('Error updating task: $e');
      return false;
    }
  }

  // Deletar task
  Future<bool> deleteTask(
    String eventId,
    String taskId,
    String deleterUid,
  ) async {
    try {
      // Verificar se o deleter é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(deleterUid)) return false;

      // Deletar todas as microtasks primeiro
      final microtasksSnapshot = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .get();

      final batch = _firestore.batch();
      
      for (final doc in microtasksSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Deletar a task
      batch.delete(_firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId));

      await batch.commit();
      return true;
    } catch (e) {
      print('Error deleting task: $e');
      return false;
    }
  }
}
