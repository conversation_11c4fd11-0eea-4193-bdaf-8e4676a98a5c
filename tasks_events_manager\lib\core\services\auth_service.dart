import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../exceptions/auth_exception.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? get currentUser => _auth.currentUser;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Cria uma instância do provedor de autenticação do Google
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // Adiciona escopos para solicitar permissões adicionais
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Você pode adicionar parâmetros customizados se necessário
      // googleProvider.setCustomParameters({'login_hint': '<EMAIL>'});

      // Faz o login com um Pop-up para web. O Firebase gerencia o fluxo
      final UserCredential userCredential = await _auth.signInWithPopup(
        googleProvider,
      );

      // Criar ou atualizar documento do usuário no Firestore
      if (userCredential.user != null) {
        await _createOrUpdateUserDocument(userCredential.user!);
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      // Trata erros específicos do Firebase Auth
      throw AuthException('Erro ao fazer login com Google: ${e.message}');
    } catch (e) {
      // Trata outros possíveis erros
      throw AuthException('Erro ao fazer login com Google: $e');
    }
  }

  Future<void> signOut() async {
    await Future.wait([_auth.signOut(), _googleSignIn.signOut()]);
  }

  Future<void> _createOrUpdateUserDocument(User user) async {
    final userDoc = _firestore.collection('users').doc(user.uid);

    final docSnapshot = await userDoc.get();

    if (!docSnapshot.exists) {
      // Create new user document
      final userModel = UserModel(
        uid: user.uid,
        displayName: user.displayName ?? '',
        email: user.email ?? '',
        photoURL: user.photoURL ?? '',
        skills: [],
        resources: [],
        availability: {},
        managerInEvents: [],
      );

      await userDoc.set(userModel.toMap());
    } else {
      // Update existing user document with latest info
      await userDoc.update({
        'displayName': user.displayName ?? '',
        'email': user.email ?? '',
        'photoURL': user.photoURL ?? '',
      });
    }
  }

  Future<UserModel?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  Future<void> updateUserData(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.uid).update(user.toMap());
    } catch (e) {
      print('Error updating user data: $e');
      rethrow;
    }
  }
}
