import 'package:flutter/foundation.dart';
import '../../../core/models/models.dart';
import '../../../core/services/services.dart';

class EventDetailProvider extends ChangeNotifier {
  final EventService _eventService = EventService();
  
  EventModel? _event;
  bool _isLoading = false;
  String? _errorMessage;

  EventModel? get event => _event;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> loadEvent(String eventId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final event = await _eventService.getEventById(eventId);
      
      if (event != null) {
        _event = event;
      } else {
        _setError('Evento não encontrado');
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('Erro ao carregar evento: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> updateEvent(EventModel updatedEvent, String updaterUid) async {
    try {
      _setError(null);
      
      final success = await _eventService.updateEvent(updatedEvent, updaterUid);
      
      if (success) {
        _event = updatedEvent;
        notifyListeners();
      } else {
        _setError('Erro ao atualizar evento');
      }
      
      return success;
    } catch (e) {
      _setError('Erro ao atualizar evento: ${e.toString()}');
      return false;
    }
  }

  Future<bool> promoteToManager(String uid, String promoterUid) async {
    if (_event == null) return false;
    
    try {
      _setError(null);
      
      final success = await _eventService.promoteToManager(_event!.eventId, uid, promoterUid);
      
      if (success) {
        // Reload event to get updated data
        await loadEvent(_event!.eventId);
      } else {
        _setError('Erro ao promover usuário');
      }
      
      return success;
    } catch (e) {
      _setError('Erro ao promover usuário: ${e.toString()}');
      return false;
    }
  }

  Future<bool> removeParticipant(String participantUid, String removerUid) async {
    if (_event == null) return false;
    
    try {
      _setError(null);
      
      final success = await _eventService.removeParticipant(_event!.eventId, participantUid, removerUid);
      
      if (success) {
        // Reload event to get updated data
        await loadEvent(_event!.eventId);
      } else {
        _setError('Erro ao remover participante');
      }
      
      return success;
    } catch (e) {
      _setError('Erro ao remover participante: ${e.toString()}');
      return false;
    }
  }

  void clearError() {
    _setError(null);
  }

  void dispose() {
    super.dispose();
  }
}
