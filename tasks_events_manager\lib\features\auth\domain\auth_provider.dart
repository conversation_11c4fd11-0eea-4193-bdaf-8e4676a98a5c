import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/models/models.dart';
import '../../../core/services/services.dart';
import '../../../core/exceptions/auth_exception.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  AuthState _state = AuthState.initial;
  User? _user;
  UserModel? _userModel;
  String? _errorMessage;

  AuthState get state => _state;
  User? get user => _user;
  UserModel? get userModel => _userModel;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _authService.authStateChanges.listen((User? user) async {
      if (user != null) {
        _user = user;
        await _loadUserData();
        _setState(AuthState.authenticated);
      } else {
        _user = null;
        _userModel = null;
        _setState(AuthState.unauthenticated);
      }
    });
  }

  Future<void> _loadUserData() async {
    if (_user != null) {
      _userModel = await _authService.getUserData(_user!.uid);
    }
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _setState(AuthState.error);
  }

  Future<void> signInWithGoogle() async {
    try {
      _setState(AuthState.loading);
      _errorMessage = null;

      final userCredential = await _authService.signInWithGoogle();

      if (userCredential == null) {
        _setState(AuthState.unauthenticated);
        return;
      }

      // O listener do authStateChanges vai lidar com o resto
    } on AuthException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erro inesperado ao fazer login: ${e.toString()}');
    }
  }

  Future<void> signOut() async {
    try {
      _setState(AuthState.loading);
      await _authService.signOut();
      // O listener do authStateChanges vai lidar com o resto
    } catch (e) {
      _setError('Erro ao fazer logout: ${e.toString()}');
    }
  }

  Future<void> updateUserData(UserModel updatedUser) async {
    try {
      await _authService.updateUserData(updatedUser);
      _userModel = updatedUser;
      notifyListeners();
    } catch (e) {
      _setError('Erro ao atualizar dados: ${e.toString()}');
    }
  }

  void clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _setState(
        _user != null ? AuthState.authenticated : AuthState.unauthenticated,
      );
    }
  }
}
