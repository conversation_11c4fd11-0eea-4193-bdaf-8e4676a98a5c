import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';
import '../constants/firebase_constants.dart';

class MicroTaskService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  // Criar nova microtask
  Future<MicroTaskModel> createMicroTask({
    required String eventId,
    required String parentTaskId,
    required String title,
    required String description,
    String? assignedVolunteer,
  }) async {
    try {
      final microTaskId = _uuid.v4();
      
      final microTask = MicroTaskModel(
        microTaskId: microTaskId,
        title: title,
        description: description,
        assignedVolunteer: assignedVolunteer,
        status: TaskStatus.open,
        parentTaskId: parentTaskId,
        eventId: eventId,
        createdAt: DateTime.now(),
      );

      // Salvar microtask como subcoleção da task
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(parentTaskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId)
          .set(microTask.toMap());

      return microTask;
    } catch (e) {
      print('Error creating microtask: $e');
      rethrow;
    }
  }

  // Buscar microtasks de uma task
  Stream<List<MicroTaskModel>> getTaskMicroTasks(String eventId, String taskId) {
    return _firestore
        .collection(FirebaseConstants.eventsCollection)
        .doc(eventId)
        .collection(FirebaseConstants.tasksCollection)
        .doc(taskId)
        .collection(FirebaseConstants.microtasksCollection)
        .orderBy(FirebaseConstants.createdAt, descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MicroTaskModel.fromDocument(doc))
            .toList());
  }

  // Buscar microtask por ID
  Future<MicroTaskModel?> getMicroTaskById(
    String eventId,
    String taskId,
    String microTaskId,
  ) async {
    try {
      final doc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId)
          .get();

      if (doc.exists) {
        return MicroTaskModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting microtask: $e');
      return null;
    }
  }

  // Atribuir voluntário à microtask
  Future<bool> assignVolunteerToMicroTask(
    String eventId,
    String taskId,
    String microTaskId,
    String volunteerUid,
    String assignerUid,
  ) async {
    try {
      final microTaskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId);

      // Verificar se o assigner é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(assignerUid)) return false;

      // Verificar se o voluntário é participante do evento
      if (!event.isParticipant(volunteerUid)) return false;

      // Atribuir voluntário à microtask
      await microTaskRef.update({
        FirebaseConstants.assignedVolunteer: volunteerUid,
      });

      return true;
    } catch (e) {
      print('Error assigning volunteer to microtask: $e');
      return false;
    }
  }

  // Remover atribuição da microtask
  Future<bool> unassignMicroTask(
    String eventId,
    String taskId,
    String microTaskId,
    String unassignerUid,
  ) async {
    try {
      final microTaskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId);

      // Verificar se o unassigner é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(unassignerUid)) return false;

      // Remover atribuição
      await microTaskRef.update({
        FirebaseConstants.assignedVolunteer: FieldValue.delete(),
      });

      return true;
    } catch (e) {
      print('Error unassigning microtask: $e');
      return false;
    }
  }

  // Atualizar status da microtask
  Future<bool> updateMicroTaskStatus(
    String eventId,
    String taskId,
    String microTaskId,
    TaskStatus newStatus,
    String updaterUid,
  ) async {
    try {
      final microTaskRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId);

      // Verificar permissões
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      final microTaskDoc = await microTaskRef.get();
      
      if (!microTaskDoc.exists) return false;

      final microTask = MicroTaskModel.fromDocument(microTaskDoc);

      // Verificar se o updater é gerente ou está atribuído à microtask
      if (!event.isManager(updaterUid) && !microTask.isAssignedTo(updaterUid)) {
        return false;
      }

      // Atualizar status
      await microTaskRef.update({
        FirebaseConstants.status: newStatus.value,
      });

      return true;
    } catch (e) {
      print('Error updating microtask status: $e');
      return false;
    }
  }

  // Atualizar microtask
  Future<bool> updateMicroTask(
    String eventId,
    String taskId,
    MicroTaskModel microTask,
    String updaterUid,
  ) async {
    try {
      // Verificar se o updater é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(updaterUid)) return false;

      // Atualizar microtask
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTask.microTaskId)
          .update(microTask.toMap());

      return true;
    } catch (e) {
      print('Error updating microtask: $e');
      return false;
    }
  }

  // Deletar microtask
  Future<bool> deleteMicroTask(
    String eventId,
    String taskId,
    String microTaskId,
    String deleterUid,
  ) async {
    try {
      // Verificar se o deleter é gerente do evento
      final eventDoc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (!eventDoc.exists) return false;

      final event = EventModel.fromDocument(eventDoc);
      if (!event.isManager(deleterUid)) return false;

      // Deletar microtask
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .collection(FirebaseConstants.tasksCollection)
          .doc(taskId)
          .collection(FirebaseConstants.microtasksCollection)
          .doc(microTaskId)
          .delete();

      return true;
    } catch (e) {
      print('Error deleting microtask: $e');
      return false;
    }
  }
}
