import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';
import '../constants/firebase_constants.dart';

class EventService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  // Criar novo evento
  Future<EventModel> createEvent({
    required String name,
    required String description,
    required String location,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required String creatorUid,
  }) async {
    try {
      // Gerar ID único para o evento (formato: EVT-XXXXXX)
      final eventId = 'EVT-${_uuid.v4().substring(0, 6).toUpperCase()}';
      
      final event = EventModel(
        eventId: eventId,
        name: name,
        description: description,
        location: location,
        requiredSkills: requiredSkills,
        requiredResources: requiredResources,
        managers: [creatorUid],
        volunteers: [],
        createdAt: DateTime.now(),
      );

      // Salvar evento no Firestore
      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .set(event.toMap());

      // Adicionar evento à lista de eventos gerenciados pelo usuário
      await _firestore
          .collection(FirebaseConstants.usersCollection)
          .doc(creatorUid)
          .update({
        FirebaseConstants.managerInEvents: FieldValue.arrayUnion([eventId]),
      });

      return event;
    } catch (e) {
      print('Error creating event: $e');
      rethrow;
    }
  }

  // Buscar evento por ID
  Future<EventModel?> getEventById(String eventId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId)
          .get();

      if (doc.exists) {
        return EventModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting event: $e');
      return null;
    }
  }

  // Buscar eventos onde o usuário é participante (gerente ou voluntário)
  Stream<List<EventModel>> getUserEvents(String uid) {
    return _firestore
        .collection(FirebaseConstants.eventsCollection)
        .where(Filter.or(
          Filter(FirebaseConstants.managers, arrayContains: uid),
          Filter(FirebaseConstants.volunteers, arrayContains: uid),
        ))
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => EventModel.fromDocument(doc))
            .toList());
  }

  // Entrar em evento via tag/ID
  Future<bool> joinEvent(String eventId, String uid) async {
    try {
      final eventRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId);

      final eventDoc = await eventRef.get();
      if (!eventDoc.exists) {
        return false; // Evento não encontrado
      }

      final event = EventModel.fromDocument(eventDoc);
      
      // Verificar se já é participante
      if (event.isParticipant(uid)) {
        return true; // Já é participante
      }

      // Adicionar como voluntário
      await eventRef.update({
        FirebaseConstants.volunteers: FieldValue.arrayUnion([uid]),
      });

      return true;
    } catch (e) {
      print('Error joining event: $e');
      return false;
    }
  }

  // Promover voluntário a gerente
  Future<bool> promoteToManager(String eventId, String uid, String promoterUid) async {
    try {
      final eventRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId);

      final eventDoc = await eventRef.get();
      if (!eventDoc.exists) {
        return false;
      }

      final event = EventModel.fromDocument(eventDoc);
      
      // Verificar se o promoter é gerente
      if (!event.isManager(promoterUid)) {
        return false;
      }

      // Verificar se o usuário é voluntário
      if (!event.isVolunteer(uid)) {
        return false;
      }

      // Mover de voluntário para gerente
      await eventRef.update({
        FirebaseConstants.volunteers: FieldValue.arrayRemove([uid]),
        FirebaseConstants.managers: FieldValue.arrayUnion([uid]),
      });

      // Atualizar lista de eventos gerenciados pelo usuário
      await _firestore
          .collection(FirebaseConstants.usersCollection)
          .doc(uid)
          .update({
        FirebaseConstants.managerInEvents: FieldValue.arrayUnion([eventId]),
      });

      return true;
    } catch (e) {
      print('Error promoting to manager: $e');
      return false;
    }
  }

  // Atualizar evento
  Future<bool> updateEvent(EventModel event, String updaterUid) async {
    try {
      // Verificar se o usuário é gerente
      if (!event.isManager(updaterUid)) {
        return false;
      }

      await _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(event.eventId)
          .update(event.toMap());

      return true;
    } catch (e) {
      print('Error updating event: $e');
      return false;
    }
  }

  // Remover participante do evento
  Future<bool> removeParticipant(String eventId, String participantUid, String removerUid) async {
    try {
      final eventRef = _firestore
          .collection(FirebaseConstants.eventsCollection)
          .doc(eventId);

      final eventDoc = await eventRef.get();
      if (!eventDoc.exists) {
        return false;
      }

      final event = EventModel.fromDocument(eventDoc);
      
      // Verificar se o remover é gerente
      if (!event.isManager(removerUid)) {
        return false;
      }

      // Remover das listas apropriadas
      if (event.isManager(participantUid)) {
        await eventRef.update({
          FirebaseConstants.managers: FieldValue.arrayRemove([participantUid]),
        });
        
        // Remover da lista de eventos gerenciados pelo usuário
        await _firestore
            .collection(FirebaseConstants.usersCollection)
            .doc(participantUid)
            .update({
          FirebaseConstants.managerInEvents: FieldValue.arrayRemove([eventId]),
        });
      } else if (event.isVolunteer(participantUid)) {
        await eventRef.update({
          FirebaseConstants.volunteers: FieldValue.arrayRemove([participantUid]),
        });
      }

      return true;
    } catch (e) {
      print('Error removing participant: $e');
      return false;
    }
  }
}
