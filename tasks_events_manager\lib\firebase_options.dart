// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC5gDe0YhtXoinP2Zb5NkJBDOBOITzEtck',
    appId: '1:407007104181:web:54e10aaa1ec1c479d942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    authDomain: 'contask-52156.firebaseapp.com',
    storageBucket: 'contask-52156.firebasestorage.app',
    measurementId: 'G-EWHDP21GD0',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyByenvoJbzLpxtG8J1omzZ5R1Y4JhVnPK8',
    appId: '1:407007104181:android:e761aae8bcb8debfd942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDx90MacKn-KCQQYVac0VKEtD6s0HkT24I',
    appId: '1:407007104181:ios:7128db3a9c570edad942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
    iosClientId: '407007104181-r7b04phk3opv6hi6sm1pgr77ebmfnm4i.apps.googleusercontent.com',
    iosBundleId: 'com.tasksevents.tasksEventsManager',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDx90MacKn-KCQQYVac0VKEtD6s0HkT24I',
    appId: '1:407007104181:ios:7128db3a9c570edad942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    storageBucket: 'contask-52156.firebasestorage.app',
    iosClientId: '407007104181-r7b04phk3opv6hi6sm1pgr77ebmfnm4i.apps.googleusercontent.com',
    iosBundleId: 'com.tasksevents.tasksEventsManager',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyC5gDe0YhtXoinP2Zb5NkJBDOBOITzEtck',
    appId: '1:407007104181:web:ac7d93f2675875ffd942fc',
    messagingSenderId: '407007104181',
    projectId: 'contask-52156',
    authDomain: 'contask-52156.firebaseapp.com',
    storageBucket: 'contask-52156.firebasestorage.app',
    measurementId: 'G-TPNMGSK06P',
  );

}