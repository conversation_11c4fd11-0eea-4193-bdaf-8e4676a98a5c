import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/firebase_constants.dart';

class MicroTaskModel {
  final String microTaskId;
  final String title;
  final String description;
  final String? assignedVolunteer; // uid (nullable)
  final TaskStatus status;
  final String parentTaskId; // Para referência à task pai
  final String eventId; // Para referência ao evento
  final DateTime createdAt;

  const MicroTaskModel({
    required this.microTaskId,
    required this.title,
    required this.description,
    this.assignedVolunteer,
    required this.status,
    required this.parentTaskId,
    required this.eventId,
    required this.createdAt,
  });

  // Factory constructor para criar MicroTaskModel a partir de Map
  factory MicroTaskModel.fromMap(Map<String, dynamic> map) {
    return MicroTaskModel(
      microTaskId: map['microTaskId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      assignedVolunteer: map['assignedVolunteer'],
      status: TaskStatusExtension.fromString(map['status'] ?? 'open'),
      parentTaskId: map['parentTaskId'] ?? '',
      eventId: map['eventId'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Factory constructor para criar MicroTaskModel a partir de DocumentSnapshot
  factory MicroTaskModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MicroTaskModel.fromMap({...data, 'microTaskId': doc.id});
  }

  // Converter MicroTaskModel para Map
  Map<String, dynamic> toMap() {
    return {
      'microTaskId': microTaskId,
      'title': title,
      'description': description,
      'assignedVolunteer': assignedVolunteer,
      'status': status.value,
      'parentTaskId': parentTaskId,
      'eventId': eventId,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  // Método copyWith para criar uma nova instância com alguns campos alterados
  MicroTaskModel copyWith({
    String? microTaskId,
    String? title,
    String? description,
    String? assignedVolunteer,
    TaskStatus? status,
    String? parentTaskId,
    String? eventId,
    DateTime? createdAt,
  }) {
    return MicroTaskModel(
      microTaskId: microTaskId ?? this.microTaskId,
      title: title ?? this.title,
      description: description ?? this.description,
      assignedVolunteer: assignedVolunteer ?? this.assignedVolunteer,
      status: status ?? this.status,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      eventId: eventId ?? this.eventId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Métodos de conveniência
  bool get isAssigned => assignedVolunteer != null;
  bool isAssignedTo(String uid) => assignedVolunteer == uid;
  
  MicroTaskModel assignTo(String uid) {
    return copyWith(assignedVolunteer: uid);
  }
  
  MicroTaskModel unassign() {
    return copyWith(assignedVolunteer: null);
  }
  
  MicroTaskModel updateStatus(TaskStatus newStatus) {
    return copyWith(status: newStatus);
  }
  
  bool get isOpen => status == TaskStatus.open;
  bool get isInProgress => status == TaskStatus.inProgress;
  bool get isDone => status == TaskStatus.done;

  @override
  String toString() {
    return 'MicroTaskModel(microTaskId: $microTaskId, title: $title, status: ${status.value}, assignedVolunteer: $assignedVolunteer, parentTaskId: $parentTaskId, eventId: $eventId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is MicroTaskModel &&
      other.microTaskId == microTaskId &&
      other.title == title &&
      other.description == description &&
      other.status == status &&
      other.parentTaskId == parentTaskId;
  }

  @override
  int get hashCode {
    return microTaskId.hashCode ^
      title.hashCode ^
      description.hashCode ^
      status.hashCode ^
      parentTaskId.hashCode;
  }
}
