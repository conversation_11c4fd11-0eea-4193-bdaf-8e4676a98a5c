import 'package:cloud_firestore/cloud_firestore.dart';

class EventModel {
  final String eventId; // Tag visível ao usuário (ex: EVT-ABC123)
  final String name;
  final String description;
  final String location;
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final List<String> managers; // uids
  final List<String> volunteers; // uids
  final DateTime createdAt;

  const EventModel({
    required this.eventId,
    required this.name,
    required this.description,
    required this.location,
    required this.requiredSkills,
    required this.requiredResources,
    required this.managers,
    required this.volunteers,
    required this.createdAt,
  });

  // Factory constructor para criar EventModel a partir de Map
  factory EventModel.fromMap(Map<String, dynamic> map) {
    return EventModel(
      eventId: map['eventId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      location: map['location'] ?? '',
      requiredSkills: List<String>.from(map['requiredSkills'] ?? []),
      requiredResources: List<String>.from(map['requiredResources'] ?? []),
      managers: List<String>.from(map['managers'] ?? []),
      volunteers: List<String>.from(map['volunteers'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Factory constructor para criar EventModel a partir de DocumentSnapshot
  factory EventModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return EventModel.fromMap(data);
  }

  // Converter EventModel para Map
  Map<String, dynamic> toMap() {
    return {
      'eventId': eventId,
      'name': name,
      'description': description,
      'location': location,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'managers': managers,
      'volunteers': volunteers,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  // Método copyWith para criar uma nova instância com alguns campos alterados
  EventModel copyWith({
    String? eventId,
    String? name,
    String? description,
    String? location,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    List<String>? managers,
    List<String>? volunteers,
    DateTime? createdAt,
  }) {
    return EventModel(
      eventId: eventId ?? this.eventId,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      managers: managers ?? this.managers,
      volunteers: volunteers ?? this.volunteers,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Métodos de conveniência
  bool isManager(String uid) => managers.contains(uid);
  bool isVolunteer(String uid) => volunteers.contains(uid);
  bool isParticipant(String uid) => isManager(uid) || isVolunteer(uid);
  
  List<String> get allParticipants => [...managers, ...volunteers];
  
  EventModel addManager(String uid) {
    if (!managers.contains(uid)) {
      return copyWith(managers: [...managers, uid]);
    }
    return this;
  }
  
  EventModel removeManager(String uid) {
    return copyWith(managers: managers.where((id) => id != uid).toList());
  }
  
  EventModel addVolunteer(String uid) {
    if (!volunteers.contains(uid)) {
      return copyWith(volunteers: [...volunteers, uid]);
    }
    return this;
  }
  
  EventModel removeVolunteer(String uid) {
    return copyWith(volunteers: volunteers.where((id) => id != uid).toList());
  }

  @override
  String toString() {
    return 'EventModel(eventId: $eventId, name: $name, description: $description, location: $location, managers: $managers, volunteers: $volunteers, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EventModel &&
      other.eventId == eventId &&
      other.name == name &&
      other.description == description &&
      other.location == location;
  }

  @override
  int get hashCode {
    return eventId.hashCode ^
      name.hashCode ^
      description.hashCode ^
      location.hashCode;
  }
}
