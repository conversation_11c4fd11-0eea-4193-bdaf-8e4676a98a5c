# Tasks Events Manager

Um aplicativo Flutter para gerenciar tarefas em eventos colaborativos.

## 🚀 Funcionalidades

- **Autenticação com Google** - Login seguro via Firebase Auth
- **Gerenciamento de Eventos** - Criar e participar de eventos colaborativos
- **Sistema de Tasks** - Organizar tarefas e microtarefas
- **Atribuição de Voluntários** - Gerenciar participantes e suas responsabilidades
- **Dashboard em Tempo Real** - Acompanhar progresso das atividades

## 🛠 Tecnologias

- **Flutter 3.x** - Framework de desenvolvimento
- **Firebase** - Backend (Auth, Firestore)
- **Provider** - Gerenciamento de estado
- **Go Router** - Navegação
- **Material Design 3** - Interface moderna

## 📱 Como Executar

### Pré-requisitos
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Conta Firebase (para produção)

### Desenvolvimento Local
```bash
# Clone o repositório
git clone <repository-url>
cd tasks_events_manager

# Instale as dependências
flutter pub get

# Execute o app
flutter run -d web-server --web-port=8080
```

## 🔧 Configuração do Firebase

### Para Desenvolvimento
O app está configurado com valores mock para desenvolvimento local. Você pode testar todas as funcionalidades sem configurar o Firebase.

### Para Produção

1. **Crie um projeto Firebase:**
   - Acesse [Firebase Console](https://console.firebase.google.com)
   - Crie um novo projeto
   - Ative Authentication e Firestore

2. **Configure Authentication:**
   - Vá para Authentication > Sign-in method
   - Ative "Google" como provedor
   - Configure o domínio autorizado

3. **Configure Firestore:**
   - Crie um banco Firestore
   - Configure as regras de segurança (veja seção abaixo)

4. **Atualize firebase_options.dart:**
   - Execute `flutterfire configure`
   - Ou substitua manualmente os valores em `lib/firebase_options.dart`

### Regras de Segurança Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users podem ler/escrever seus próprios dados
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Events - participantes podem ler, gerentes podem escrever
    match /events/{eventId} {
      allow read: if request.auth != null &&
        (request.auth.uid in resource.data.managers ||
         request.auth.uid in resource.data.volunteers);
      allow write: if request.auth != null &&
        request.auth.uid in resource.data.managers;

      // Tasks como subcoleção
      match /tasks/{taskId} {
        allow read: if request.auth != null;
        allow write: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/events/$(eventId)).data.managers;

        // MicroTasks como subcoleção
        match /microtasks/{microTaskId} {
          allow read, write: if request.auth != null;
        }
      }
    }
  }
}
```

## 📊 Estrutura do Projeto

```
lib/
├── core/
│   ├── constants/         # Constantes do Firebase
│   ├── exceptions/        # Exceções customizadas
│   ├── models/           # Modelos de dados
│   ├── services/         # Serviços (Firebase, Auth)
│   └── utils/            # Utilitários (Router)
├── features/
│   ├── auth/             # Autenticação
│   ├── events/           # Gerenciamento de eventos
│   └── tasks/            # Gerenciamento de tasks
└── shared_widgets/       # Widgets compartilhados
```

## 🎯 Modelos de Dados

### User
- `uid`, `displayName`, `email`, `photoURL`
- `skills[]`, `resources[]`, `availability{}`
- `managerInEvents[]`

### Event
- `eventId`, `name`, `description`, `location`
- `requiredSkills[]`, `requiredResources[]`
- `managers[]`, `volunteers[]`, `createdAt`

### Task
- `taskId`, `title`, `description`, `status`
- `requiredSkills[]`, `requiredResources[]`
- `assignedVolunteers[]`, `createdBy`, `eventId`

### MicroTask
- `microTaskId`, `title`, `description`
- `assignedVolunteer`, `status`, `parentTaskId`

## 🚦 Status do Projeto

- ✅ Autenticação com Google (Web)
- ✅ Gerenciamento de Eventos
- ✅ Interface de Dashboard
- ✅ Navegação completa
- 🔄 Gerenciamento de Tasks (em desenvolvimento)
- 🔄 Sistema de Atribuição (em desenvolvimento)

## 📝 Próximos Passos

1. Implementar CRUD completo de Tasks
2. Sistema de atribuição automática
3. Notificações em tempo real
4. Relatórios e analytics
5. App mobile (Android/iOS)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.
