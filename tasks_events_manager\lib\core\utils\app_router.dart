import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../../features/auth/presentation/auth_gate.dart';
import '../../features/events/presentation/home_page.dart';
import '../../features/events/presentation/event_shell.dart';
import '../../features/tasks/presentation/task_details_page.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: 'auth',
        builder: (context, state) => const AuthGate(),
      ),
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),
      GoRoute(
        path: '/event/:eventId',
        name: 'event',
        builder: (context, state) {
          final eventId = state.pathParameters['eventId']!;
          return EventShell(eventId: eventId);
        },
        routes: [
          GoRoute(
            path: '/task/:taskId',
            name: 'task-details',
            builder: (context, state) {
              final eventId = state.pathParameters['eventId']!;
              final taskId = state.pathParameters['taskId']!;
              return TaskDetailsPage(
                eventId: eventId,
                taskId: taskId,
              );
            },
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Página não encontrada',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'A página "${state.uri}" não existe.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Voltar ao início'),
            ),
          ],
        ),
      ),
    ),
  );
}
