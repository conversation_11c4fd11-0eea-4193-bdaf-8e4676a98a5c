import 'package:flutter/foundation.dart';
import '../../../core/models/models.dart';
import '../../../core/services/services.dart';

class EventsProvider extends ChangeNotifier {
  final EventService _eventService = EventService();
  
  List<EventModel> _events = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<EventModel> get events => _events;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> loadUserEvents(String uid) async {
    _setLoading(true);
    _setError(null);
    
    try {
      _eventService.getUserEvents(uid).listen((events) {
        _events = events;
        _setLoading(false);
      });
    } catch (e) {
      _setError('Erro ao carregar eventos: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<EventModel?> createEvent({
    required String name,
    required String description,
    required String location,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required String creatorUid,
  }) async {
    try {
      _setError(null);
      
      final event = await _eventService.createEvent(
        name: name,
        description: description,
        location: location,
        requiredSkills: requiredSkills,
        requiredResources: requiredResources,
        creatorUid: creatorUid,
      );
      
      return event;
    } catch (e) {
      _setError('Erro ao criar evento: ${e.toString()}');
      return null;
    }
  }

  Future<bool> joinEvent(String eventId, String uid) async {
    try {
      _setError(null);
      
      final success = await _eventService.joinEvent(eventId, uid);
      
      if (!success) {
        _setError('Evento não encontrado ou erro ao entrar');
      }
      
      return success;
    } catch (e) {
      _setError('Erro ao entrar no evento: ${e.toString()}');
      return false;
    }
  }

  void clearError() {
    _setError(null);
  }
}
