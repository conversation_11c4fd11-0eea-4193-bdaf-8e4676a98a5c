_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.58/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.58/lib/
async
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib/
cloud_firestore
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.11/lib/
cloud_firestore_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.11/lib/
cloud_firestore_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.11/lib/
collection
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib/
firebase_auth
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.6.2/lib/
firebase_auth_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.2/lib/
firebase_auth_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.2/lib/
firebase_core
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.1/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_lints
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0/lib/
go_router
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-16.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-16.0.0/lib/
google_identity_services_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_sign_in
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-7.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-7.1.0/lib/
google_sign_in_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-7.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-7.0.2/lib/
google_sign_in_ios
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-6.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-6.0.1/lib/
google_sign_in_platform_interface
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/
google_sign_in_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-1.0.0/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
path
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/lib/
source_span
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
stack_trace
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib/
term_glyph
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
tasks_events_manager
3.8
file:///C:/Users/<USER>/Documents/augment-projects/contask2/tasks_events_manager/
file:///C:/Users/<USER>/Documents/augment-projects/contask2/tasks_events_manager/lib/
sky_engine
3.7
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/bin/cache/pkg/sky_engine/
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter/
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter/lib/
flutter_test
3.7
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter_test/
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter_web_plugins/
file:///C:/Users/<USER>/Documents/victor_teste/projetoTCC/flutter/packages/flutter_web_plugins/lib/
2
