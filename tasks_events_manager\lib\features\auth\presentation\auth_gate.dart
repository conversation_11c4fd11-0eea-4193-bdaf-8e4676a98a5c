import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../domain/auth_provider.dart';
import 'login_page.dart';
import 'splash_screen.dart';
import '../../events/presentation/home_page.dart';

class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.state) {
          case AuthState.initial:
          case AuthState.loading:
            return const SplashScreen();
            
          case AuthState.authenticated:
            return const HomePage();
            
          case AuthState.unauthenticated:
          case AuthState.error:
            return const LoginPage();
        }
      },
    );
  }
}
