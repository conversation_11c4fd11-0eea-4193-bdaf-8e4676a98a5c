import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/firebase_constants.dart';

class TaskModel {
  final String taskId;
  final String title;
  final String description;
  final TaskStatus status;
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final List<String> assignedVolunteers; // uids
  final String createdBy; // uid
  final DateTime createdAt;
  final String eventId; // Para referência ao evento pai

  const TaskModel({
    required this.taskId,
    required this.title,
    required this.description,
    required this.status,
    required this.requiredSkills,
    required this.requiredResources,
    required this.assignedVolunteers,
    required this.createdBy,
    required this.createdAt,
    required this.eventId,
  });

  // Factory constructor para criar TaskModel a partir de Map
  factory TaskModel.fromMap(Map<String, dynamic> map) {
    return TaskModel(
      taskId: map['taskId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      status: TaskStatusExtension.fromString(map['status'] ?? 'open'),
      requiredSkills: List<String>.from(map['requiredSkills'] ?? []),
      requiredResources: List<String>.from(map['requiredResources'] ?? []),
      assignedVolunteers: List<String>.from(map['assignedVolunteers'] ?? []),
      createdBy: map['createdBy'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      eventId: map['eventId'] ?? '',
    );
  }

  // Factory constructor para criar TaskModel a partir de DocumentSnapshot
  factory TaskModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TaskModel.fromMap({...data, 'taskId': doc.id});
  }

  // Converter TaskModel para Map
  Map<String, dynamic> toMap() {
    return {
      'taskId': taskId,
      'title': title,
      'description': description,
      'status': status.value,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'assignedVolunteers': assignedVolunteers,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'eventId': eventId,
    };
  }

  // Método copyWith para criar uma nova instância com alguns campos alterados
  TaskModel copyWith({
    String? taskId,
    String? title,
    String? description,
    TaskStatus? status,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    List<String>? assignedVolunteers,
    String? createdBy,
    DateTime? createdAt,
    String? eventId,
  }) {
    return TaskModel(
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      assignedVolunteers: assignedVolunteers ?? this.assignedVolunteers,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      eventId: eventId ?? this.eventId,
    );
  }

  // Métodos de conveniência
  bool isAssignedTo(String uid) => assignedVolunteers.contains(uid);
  
  TaskModel assignVolunteer(String uid) {
    if (!assignedVolunteers.contains(uid)) {
      return copyWith(assignedVolunteers: [...assignedVolunteers, uid]);
    }
    return this;
  }
  
  TaskModel unassignVolunteer(String uid) {
    return copyWith(
      assignedVolunteers: assignedVolunteers.where((id) => id != uid).toList(),
    );
  }
  
  TaskModel updateStatus(TaskStatus newStatus) {
    return copyWith(status: newStatus);
  }
  
  bool get isOpen => status == TaskStatus.open;
  bool get isInProgress => status == TaskStatus.inProgress;
  bool get isDone => status == TaskStatus.done;

  @override
  String toString() {
    return 'TaskModel(taskId: $taskId, title: $title, status: ${status.value}, assignedVolunteers: $assignedVolunteers, createdBy: $createdBy, eventId: $eventId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TaskModel &&
      other.taskId == taskId &&
      other.title == title &&
      other.description == description &&
      other.status == status;
  }

  @override
  int get hashCode {
    return taskId.hashCode ^
      title.hashCode ^
      description.hashCode ^
      status.hashCode;
  }
}
