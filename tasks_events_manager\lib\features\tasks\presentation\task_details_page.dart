import 'package:flutter/material.dart';

class TaskDetailsPage extends StatelessWidget {
  final String eventId;
  final String taskId;

  const TaskDetailsPage({
    super.key,
    required this.eventId,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Task $taskId'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.task, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'Detalhes da Task',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text('Evento: $eventId'),
            Text('Task: $taskId'),
            const SizedBox(height: 16),
            const Text('Em desenvolvimento...'),
          ],
        ),
      ),
    );
  }
}
