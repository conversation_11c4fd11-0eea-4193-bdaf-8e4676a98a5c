class FirebaseConstants {
  // Collection names
  static const String usersCollection = 'users';
  static const String eventsCollection = 'events';
  static const String tasksCollection = 'tasks';
  static const String microtasksCollection = 'microtasks';
  
  // Field names
  static const String uid = 'uid';
  static const String displayName = 'displayName';
  static const String email = 'email';
  static const String photoURL = 'photoURL';
  static const String skills = 'skills';
  static const String resources = 'resources';
  static const String availability = 'availability';
  static const String managerInEvents = 'managerInEvents';
  
  // Event fields
  static const String eventId = 'eventId';
  static const String name = 'name';
  static const String description = 'description';
  static const String location = 'location';
  static const String requiredSkills = 'requiredSkills';
  static const String requiredResources = 'requiredResources';
  static const String managers = 'managers';
  static const String volunteers = 'volunteers';
  static const String createdAt = 'createdAt';
  
  // Task fields
  static const String taskId = 'taskId';
  static const String title = 'title';
  static const String status = 'status';
  static const String assignedVolunteers = 'assignedVolunteers';
  static const String createdBy = 'createdBy';
  
  // MicroTask fields
  static const String microTaskId = 'microTaskId';
  static const String assignedVolunteer = 'assignedVolunteer';
  static const String parentTaskId = 'parentTaskId';
}

enum TaskStatus {
  open,
  inProgress,
  done,
}

extension TaskStatusExtension on TaskStatus {
  String get value {
    switch (this) {
      case TaskStatus.open:
        return 'open';
      case TaskStatus.inProgress:
        return 'in_progress';
      case TaskStatus.done:
        return 'done';
    }
  }
  
  static TaskStatus fromString(String status) {
    switch (status) {
      case 'open':
        return TaskStatus.open;
      case 'in_progress':
        return TaskStatus.inProgress;
      case 'done':
        return TaskStatus.done;
      default:
        return TaskStatus.open;
    }
  }
}
