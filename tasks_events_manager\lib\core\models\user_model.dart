import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String displayName;
  final String email;
  final String photoURL;
  final List<String> skills;
  final List<String> resources;
  final Map<String, List<String>> availability; // {"Mon": ["18:00-22:00"], ...}
  final List<String> managerInEvents; // IDs de eventos onde é gerente

  const UserModel({
    required this.uid,
    required this.displayName,
    required this.email,
    required this.photoURL,
    required this.skills,
    required this.resources,
    required this.availability,
    required this.managerInEvents,
  });

  // Factory constructor para criar UserModel a partir de Map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      displayName: map['displayName'] ?? '',
      email: map['email'] ?? '',
      photoURL: map['photoURL'] ?? '',
      skills: List<String>.from(map['skills'] ?? []),
      resources: List<String>.from(map['resources'] ?? []),
      availability: Map<String, List<String>>.from(
        (map['availability'] ?? {}).map(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      ),
      managerInEvents: List<String>.from(map['managerInEvents'] ?? []),
    );
  }

  // Factory constructor para criar UserModel a partir de DocumentSnapshot
  factory UserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromMap(data);
  }

  // Converter UserModel para Map
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'displayName': displayName,
      'email': email,
      'photoURL': photoURL,
      'skills': skills,
      'resources': resources,
      'availability': availability,
      'managerInEvents': managerInEvents,
    };
  }

  // Método copyWith para criar uma nova instância com alguns campos alterados
  UserModel copyWith({
    String? uid,
    String? displayName,
    String? email,
    String? photoURL,
    List<String>? skills,
    List<String>? resources,
    Map<String, List<String>>? availability,
    List<String>? managerInEvents,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      photoURL: photoURL ?? this.photoURL,
      skills: skills ?? this.skills,
      resources: resources ?? this.resources,
      availability: availability ?? this.availability,
      managerInEvents: managerInEvents ?? this.managerInEvents,
    );
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, displayName: $displayName, email: $email, skills: $skills, resources: $resources, availability: $availability, managerInEvents: $managerInEvents)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel &&
      other.uid == uid &&
      other.displayName == displayName &&
      other.email == email &&
      other.photoURL == photoURL;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
      displayName.hashCode ^
      email.hashCode ^
      photoURL.hashCode;
  }
}
